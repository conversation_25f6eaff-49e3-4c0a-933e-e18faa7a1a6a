"use server";

import { getTranslations } from "next-intl/server";
import { ContactFormData, contactFormSchema } from "./_contact/model";
import { sendEmail } from "@/lib/mailer";

const contactEmailTemplate = ({ name, email, whatsapp }: ContactFormData) => `
<div style="font-family: Arial, sans-serif; max-width: 500px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
    <div style="background-color: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2 style="color: #333; margin-top: 0; text-align: center;">New Contact Form Submission</h2>

        <div style="margin: 20px 0;">
            <p style="margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                <strong>Name:</strong> ${name}
            </p>
            <p style="margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                <strong>Email:</strong> ${email}
            </p>
            <p style="margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                <strong>WhatsApp:</strong> ${whatsapp}
            </p>
        </div>

        <p style="text-align: center; color: #666; font-size: 12px; margin-top: 20px;">
            Received on ${new Date().toLocaleDateString()}
        </p>
    </div>
</div>
`;

const sendContactEmail = async (payload: ContactFormData) => {
  const tValidation = await getTranslations("validation");

  try {
    const schema = contactFormSchema(tValidation).parse(payload);

    await sendEmail({
      to: "<EMAIL>",
      subject: "🌟 New Contact Form Submission - Rose Palhares",
      html: contactEmailTemplate(schema),
    });
  } catch (error) {
    console.error("Failed to send contact email:", error);
    throw error;
  }
};

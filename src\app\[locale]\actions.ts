"use server";

import { getTranslations } from "next-intl/server";
import { ContactFormData, contactFormSchema } from "./_contact/model";
import { sendEmail } from "@/lib/mailer";

const contactEmailTemplate = ({ name, email, whatsapp }: ContactFormData) => `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Contact Form Submission</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: #ffffff;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header p {
            color: #6c757d;
            margin: 5px 0 0 0;
            font-size: 14px;
        }
        .contact-info {
            margin: 20px 0;
        }
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 80px;
            margin-right: 15px;
        }
        .info-value {
            color: #212529;
            word-break: break-word;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
        }
        .timestamp {
            background-color: #e3f2fd;
            padding: 10px;
            border-radius: 6px;
            margin-top: 20px;
            text-align: center;
            font-size: 12px;
            color: #1565c0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 New Contact Form Submission</h1>
            <p>Someone has reached out through your website</p>
        </div>

        <div class="contact-info">
            <div class="info-item">
                <span class="info-label">👤 Name:</span>
                <span class="info-value">${name}</span>
            </div>

            <div class="info-item">
                <span class="info-label">📧 Email:</span>
                <span class="info-value">${email}</span>
            </div>

            <div class="info-item">
                <span class="info-label">📱 WhatsApp:</span>
                <span class="info-value">${whatsapp}</span>
            </div>
        </div>

        <div class="timestamp">
            Received on ${new Date().toLocaleString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZoneName: 'short'
            })}
        </div>

        <div class="footer">
            <p>This email was automatically generated from your website's contact form.</p>
        </div>
    </div>
</body>
</html>
`;


const sendContactEmail = async (payload: ContactFormData) => {
    const tValidation = await getTranslations("validation");

    try {
        const schema = contactFormSchema(tValidation).parse(payload);
        
        await sendEmail({
            to: "<EMAIL>",
            subject: "New contact form submission",
            html: `
                <p>Name: ${schema.name}</p>
                <p>Email: ${schema.email}</p>
                <p>WhatsApp: ${schema.whatsapp}</p>
            `,
        });
        
    } catch (error) {
        
    }
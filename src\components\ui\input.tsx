import * as React from "react";

import { cn } from "@/lib/utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-secondary-foreground/80 selection:bg-primary selection:text-primary-foreground border-secondary-foreground flex w-full min-w-0 border-b-2 bg-transparent px-1 text-base duration-300 ease-out outline-none file:inline-flex file:bg-transparent file:text-sm file:font-medium placeholder:text-base disabled:cursor-not-allowed disabled:opacity-50 sm:text-lg",
        "focus-visible:border-primary",
        "aria-invalid:border-destructive",
        className,
      )}
      {...props}
    />
  );
}

export { Input };
